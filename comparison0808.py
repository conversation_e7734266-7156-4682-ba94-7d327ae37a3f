import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import time
from scipy.spatial import KDTree
import seaborn as sns
import sys
from matplotlib import rcParams

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def debug_print(title, data=None, shape_only=False):
    """增强的调试打印"""
    print(f"\n=== {title} ===")
    if data is not None:
        if isinstance(data, dict):
            print("字典内容:")
            for k, v in data.items():
                print(f"{k}: {v.shape if isinstance(v, np.ndarray) else v}")
        else:
            print(f"形状: {data.shape}")
            if not shape_only:
                print(data[:2] if len(data) > 2 else data)
    print("-" * 40)


def load_points(filepath):
    """加载点云数据"""
    print(f"\n[加载] {filepath}")
    try:
        data = np.loadtxt(filepath, delimiter=',')
        assert data.shape[1] >= 3, "数据必须包含XYZ坐标"
        return data[:, :3]
    except Exception as e:
        print(f"加载失败: {str(e)}")
        raise


def load_reference_points(filepath):
    """加载六点定位参考点"""
    print(f"\n[加载参考点] {filepath}")
    try:
        data = np.loadtxt(filepath, dtype=str, delimiter='\t')
        return {row[3]: row[:3].astype(float) for row in data}
    except Exception as e:
        print(f"参考点加载失败: {str(e)}")
        raise


def apply_transform(points, transform):
    """应用变换矩阵"""
    try:
        assert transform.shape == (4, 4), "变换矩阵必须是4x4"
        homogenous = np.column_stack([points[:, :3], np.ones(len(points))])
        return (transform @ homogenous.T).T[:, :3]
    except Exception as e:
        print(f"变换失败: {str(e)}")
        raise


def coarse_alignment(source, target, ref_points):
    """六点定位粗配准"""
    print("\n[六点定位] 开始...")
    try:
        source_points = np.array([ref_points[k] for k in ['A1', 'A2', 'A3', 'B4', 'B5', 'C6']])

        target_pcd = o3d.geometry.PointCloud(o3d.utility.Vector3dVector(target))
        kdtree = o3d.geometry.KDTreeFlann(target_pcd)
        target_points = np.array([target[kdtree.search_knn_vector_3d(p, 1)[1][0]] for p in source_points])

        centroid_s = source_points.mean(axis=0)
        centroid_t = target_points.mean(axis=0)
        H = (source_points - centroid_s).T @ (target_points - centroid_t)
        U, _, Vt = np.linalg.svd(H)
        rotation = Vt.T @ U.T

        if np.linalg.det(rotation) < 0:
            Vt[2, :] *= -1
            rotation = Vt.T @ U.T

        transform = np.eye(4)
        transform[:3, :3] = rotation
        transform[:3, 3] = centroid_t - rotation @ centroid_s

        debug_print("六点定位结果", transform)
        return transform

    except Exception as e:
        print(f"六点定位失败: {str(e)}")
        raise


def icp_refinement(source, target, initial_transform=None):
    """ICP精配准"""
    print("\n[ICP精配准] 开始...")
    try:
        source_pcd = o3d.geometry.PointCloud(o3d.utility.Vector3dVector(source))
        target_pcd = o3d.geometry.PointCloud(o3d.utility.Vector3dVector(target))

        voxel_size = max(target_pcd.get_axis_aligned_bounding_box().get_extent()) / 50
        source_down = source_pcd.voxel_down_sample(voxel_size)
        target_down = target_pcd.voxel_down_sample(voxel_size)
        print(f"降采样后点数: 源={len(source_down.points)}, 目标={len(target_down.points)}")

        reg_result = o3d.pipelines.registration.registration_icp(
            source_down, target_down,
            max_correspondence_distance=2 * voxel_size,
            init=np.eye(4) if initial_transform is None else initial_transform,
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=2000)
        )
        print(f"ICP完成, 误差={reg_result.inlier_rmse:.4f}mm")
        return reg_result.transformation

    except Exception as e:
        print(f"ICP失败: {str(e)}")
        raise


def slice_by_y(points, y_values, thickness=1.0):
    """按Y值切片（厚度1mm）"""
    print(f"\n[切片] 厚度={thickness}mm")
    slices = []
    for i, y in enumerate(y_values):
        print(f"处理Y={y}mm ({i + 1}/{len(y_values)})...", end=' ')
        mask = (points[:, 1] >= y - thickness / 2) & (points[:, 1] <= y + thickness / 2)
        slice_xz = points[mask][:, [0, 2]]
        print(f"获得{len(slice_xz)}个点")
        if len(slice_xz) > 0:
            slices.append((y, slice_xz))
    return slices


def calculate_2d_errors(blade_slice, cad_slice):
    """计算二维误差"""
    tree = KDTree(cad_slice)
    distances, _ = tree.query(blade_slice)
    return {
        'mean_error': np.mean(distances),
        'max_error': np.max(distances),
        'std_error': np.std(distances),
        'error_distribution': distances
    }


def plot_slice_comparison(y, blade_slice, cad_slice, errors):
    """绘制切面对比图（修改后的热力图）"""
    print(f"生成Y={y}mm对比图...", end=' ')

    # 过滤过大误差（保留99%的数据）
    error_threshold = np.percentile(errors['error_distribution'], 99)
    valid_mask = errors['error_distribution'] <= error_threshold
    filtered_blade = blade_slice[valid_mask]
    filtered_errors = errors['error_distribution'][valid_mask]

    fig = plt.figure(figsize=(15, 6))

    # 点云对比
    plt.subplot(1, 2, 1)
    plt.scatter(cad_slice[:, 0], cad_slice[:, 1], s=1, c='green', label='CAD模型')
    plt.scatter(blade_slice[:, 0], blade_slice[:, 1], s=1, c='red', label='叶片数据')
    plt.title(f'Y={y}mm - 点云对比 (点数: 叶片={len(blade_slice)}, CAD={len(cad_slice)})')
    plt.xlabel('X (mm)')
    plt.ylabel('Z (mm)')
    plt.legend()
    plt.axis('equal')

    # 修改后的热力图
    plt.subplot(1, 2, 2)
    if len(filtered_errors) > 0:
        scatter = plt.scatter(
            filtered_blade[:, 0], filtered_blade[:, 1],
            c=filtered_errors,
            cmap="jet",
            s=30,
            vmin=0,
            vmax=max(0.2, np.max(filtered_errors)),
            alpha=0.8
        )
        plt.colorbar(scatter, label="误差 (mm)")
        plt.title(f'Y={y}mm - 误差热力图 (过滤后)')
        plt.xlabel('X (mm)')
        plt.ylabel('Z (mm)')
        plt.grid(True, linestyle='--', alpha=0.5)
    else:
        plt.text(0.5, 0.5, "无有效误差数据", ha='center', va='center')
        plt.title(f'Y={y}mm - 无有效误差数据')

    plt.axis('equal')
    plt.tight_layout()
    print("完成")
    return fig


def generate_report(y_values, results, timers, file_info, output_path):
    """生成PDF报告"""
    print("\n[生成报告] 开始...")
    try:
        with PdfPages(output_path) as pdf:
            # 第一页：摘要
            print("创建摘要页...", end=' ')
            plt.figure(figsize=(11, 8.5))
            plt.axis('off')

            summary = [
                "=== 叶片检测报告 ===",
                f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                "\n[输入数据]",
                f"叶片点数: {file_info['blade_count']}",
                f"CAD点数: {file_info['cad_count']}",
                f"分析Y值: {y_values}",
                "\n[处理时间]",
                f"六点定位: {timers['coarse']:.2f}s",
                f"ICP配准: {timers['icp']:.2f}s",
                f"切片分析: {timers['slice']:.2f}s",
                "\n[整体误差]",
                f"平均误差: {np.mean([r['errors']['mean_error'] for r in results]):.4f}mm",
                f"最大误差: {np.max([r['errors']['max_error'] for r in results]):.4f}mm"
            ]

            plt.text(0.1, 0.95, "\n".join(summary), fontsize=12, va='top')
            pdf.savefig(bbox_inches='tight')
            plt.close()
            print("完成")

            # 各切片结果页
            print(f"生成{len(results)}个切片结果页...")
            for i, result in enumerate(results):
                print(f"正在处理 {i + 1}/{len(results)}: Y={result['y']}mm...", end=' ')
                fig = plot_slice_comparison(
                    result['y'],
                    result['blade_slice'],
                    result['cad_slice'],
                    result['errors']
                )
                pdf.savefig(fig, bbox_inches='tight')
                plt.close(fig)

        print(f"\n报告生成完成: {output_path}")

    except Exception as e:
        print(f"\n报告生成失败: {str(e)}")
        raise


def main():
    try:
        # 配置参数
        y_values = [310, 325, 340, 355, 370]
        input_files = {
            'blade': r"bladedata.txt",
            'cad': r"cad_dense.txt",
            'ref_points': r"大东风二涡六点.txt"
        }
        output_pdf = r"final_report.pdf"

        # 计时器
        timers = {
            'total': time.time(),
            'load': 0,
            'coarse': 0,
            'icp': 0,
            'slice': 0,
            'report': 0
        }

        # === 1. 数据加载 ===
        print("\n" + "=" * 50)
        print("阶段1: 数据加载")
        load_start = time.time()
        blade_data = load_points(input_files['blade'])
        cad_data = load_points(input_files['cad'])
        ref_points = load_reference_points(input_files['ref_points'])
        timers['load'] = time.time() - load_start

        # === 2. 初始变换 ===
        initial_rt = np.array([
            [1.883110476355591e-01, 3.190023408460866e-02, 9.815912206227934e-01, -6.894818114040970e+01],
            [1.082137947646718e-02, -9.994790641450655e-01, 3.040556004961141e-02, 4.196732026553934e+02],
            [9.820498190441381e-01, 4.896468222239632e-03, -1.885581541973832e-01, 2.953095276327016e+01],
            [0.0, 0.0, 0.0, 1.0]
        ])
        transformed_blade = apply_transform(blade_data, initial_rt)

        # === 3. 六点定位 ===
        print("\n" + "=" * 50)
        print("阶段3: 六点定位")
        coarse_start = time.time()
        coarse_transform = coarse_alignment(transformed_blade, cad_data, ref_points)
        coarse_aligned = apply_transform(transformed_blade, coarse_transform)
        timers['coarse'] = time.time() - coarse_start

        # === 4. ICP精配准 ===
        print("\n" + "=" * 50)
        print("阶段4: ICP精配准")
        icp_start = time.time()
        final_transform = icp_refinement(coarse_aligned, cad_data)
        final_aligned = apply_transform(coarse_aligned, final_transform)
        timers['icp'] = time.time() - icp_start

        # === 5. 切片分析 ===
        print("\n" + "=" * 50)
        print("阶段5: 切片分析")
        slice_start = time.time()
        blade_slices = slice_by_y(final_aligned, y_values)
        cad_slices = slice_by_y(cad_data, y_values)

        results = []
        for (y, blade_slice), (_, cad_slice) in zip(blade_slices, cad_slices):
            errors = calculate_2d_errors(blade_slice, cad_slice)
            results.append({
                'y': y,
                'blade_slice': blade_slice,
                'cad_slice': cad_slice,
                'errors': errors
            })
        timers['slice'] = time.time() - slice_start

        # === 6. 生成报告 ===
        print("\n" + "=" * 50)
        print("阶段6: 生成报告")
        report_start = time.time()
        file_info = {
            'blade_count': len(blade_data),
            'cad_count': len(cad_data)
        }
        generate_report(y_values, results, timers, file_info, output_pdf)
        timers['report'] = time.time() - report_start

        # === 完成 ===
        timers['total'] = time.time() - timers['total']
        print("\n" + "=" * 50)
        print("处理完成!")
        print(f"报告已保存: {output_pdf}")
        print("\n时间统计:")
        for k, v in timers.items():
            print(f"{k}: {v:.2f}s")

    except Exception as e:
        print("\n!!! 处理中断 !!!")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误位置: {sys.exc_info()[2].tb_lineno}")
        print(f"错误信息: {str(e)}")
        raise


if __name__ == "__main__":
    main()